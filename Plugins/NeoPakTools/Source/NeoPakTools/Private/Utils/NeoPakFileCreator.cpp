// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/NeoPakFileCreator.h"
#include "NeoPakTools.h"
#include "Config/NeoPakToolsSettings.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformProcess.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/IAssetRegistry.h"
#include "Engine/Engine.h"
#include "Misc/PackageName.h"

bool FNeoPakFileCreator::CreatePakFromConfig(UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    // Get assets to package
    TArray<FSoftObjectPath> SoftAssetPaths = ConfigAsset->GetAssetsToPackage();
    TArray<FString> AssetPaths;
    
    for (const FSoftObjectPath& SoftPath : SoftAssetPaths)
    {
        AssetPaths.Add(SoftPath.ToString());
    }

    if (AssetPaths.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No assets to package for config: %s. Please ensure you have set the required asset(s) in the configuration."), *ConfigAsset->ConfigName);
        return false;
    }

    // Get output path
    FString OutputPath = ConfigAsset->GetFullOutputPath();

    return CreatePakFromAssets(AssetPaths, OutputPath);
}

bool FNeoPakFileCreator::CreatePakFromAssets(const TArray<FString>& AssetPaths, const FString& OutputPath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK file: %s"), *OutputPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Assets to package: %d"), AssetPaths.Num());

    // Validate UnrealPak availability
    if (!IsUnrealPakAvailable())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak is not available"));
        return false;
    }

    // Validate asset paths
    TArray<FString> MissingAssets;
    if (!ValidateAssetPaths(AssetPaths, MissingAssets))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Some assets are missing:"));
        for (const FString& MissingAsset : MissingAssets)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("  - %s"), *MissingAsset);
        }
        return false;
    }

    // Ensure output directory exists
    FString OutputDirectory = FPaths::GetPath(OutputPath);
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*OutputDirectory))
    {
        if (!FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*OutputDirectory))
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create output directory: %s"), *OutputDirectory);
            return false;
        }
    }

    // Create response file
    FString ResponseFilePath = GenerateResponseFilePath(FPaths::GetBaseFilename(OutputPath));
    if (!CreateResponseFile(AssetPaths, ResponseFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create response file: %s"), *ResponseFilePath);
        return false;
    }

    // Execute UnrealPak
    bool bSuccess = ExecuteUnrealPak(OutputPath, ResponseFilePath);

    // Clean up temporary files
    CleanupTempFiles(ResponseFilePath);

    if (bSuccess)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully created PAK file: %s"), *OutputPath);
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create PAK file: %s"), *OutputPath);
    }

    return bSuccess;
}

FString FNeoPakFileCreator::GetUnrealPakPath()
{
    // Try to find UnrealPak in the engine binaries directory
    FString EngineBinariesPath = FPaths::EngineDir() / TEXT("Binaries");
    
#if PLATFORM_WINDOWS
    FString UnrealPakPath = EngineBinariesPath / TEXT("Win64") / TEXT("UnrealPak.exe");
#elif PLATFORM_MAC
    FString UnrealPakPath = EngineBinariesPath / TEXT("Mac") / TEXT("UnrealPak");
#elif PLATFORM_LINUX
    FString UnrealPakPath = EngineBinariesPath / TEXT("Linux") / TEXT("UnrealPak");
#else
    FString UnrealPakPath = TEXT("");
#endif

    return UnrealPakPath;
}

bool FNeoPakFileCreator::IsUnrealPakAvailable()
{
    FString UnrealPakPath = GetUnrealPakPath();
    return !UnrealPakPath.IsEmpty() && FPlatformFileManager::Get().GetPlatformFile().FileExists(*UnrealPakPath);
}

bool FNeoPakFileCreator::CreateResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath)
{
    TArray<FString> ResponseLines;
    TArray<FString> FilePaths = ConvertAssetPathsToFilePaths(AssetPaths);

    UE_LOG(LogNeoPakTools, Log, TEXT("Creating response file with %d asset paths"), AssetPaths.Num());

    for (int32 i = 0; i < AssetPaths.Num() && i < FilePaths.Num(); ++i)
    {
        const FString& AssetPath = AssetPaths[i];
        const FString& FilePath = FilePaths[i];

        UE_LOG(LogNeoPakTools, Log, TEXT("Processing asset %d: %s -> %s"), i, *AssetPath, *FilePath);

        if (!FilePath.IsEmpty())
        {
            // 检查源文件是否存在
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
            {
                // 将资产路径转换为PAK内的路径格式
                FString PakInternalPath = AssetPath;
                if (PakInternalPath.StartsWith(TEXT("/Game/")))
                {
                    PakInternalPath = PakInternalPath.RightChop(6); // 移除"/Game/"前缀
                }

                // Format: "SourceFile" "DestinationPath"
                FString ResponseLine = FString::Printf(TEXT("\"%s\" \"%s\""), *FilePath, *PakInternalPath);
                ResponseLines.Add(ResponseLine);
                UE_LOG(LogNeoPakTools, Log, TEXT("Added to response file: %s"), *ResponseLine);
            }
            else
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Source file does not exist: %s"), *FilePath);
            }
        }
        else
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Empty file path for asset: %s"), *AssetPath);
        }
    }

    if (ResponseLines.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No valid file paths found for response file"));
        return false;
    }

    FString ResponseContent = FString::Join(ResponseLines, TEXT("\n"));

    UE_LOG(LogNeoPakTools, Log, TEXT("Response file content:\n%s"), *ResponseContent);

    if (!FFileHelper::SaveStringToFile(ResponseContent, *ResponseFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save response file: %s"), *ResponseFilePath);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Created response file: %s with %d entries"), *ResponseFilePath, ResponseLines.Num());
    return true;
}

bool FNeoPakFileCreator::ExecuteUnrealPak(const FString& PakFilePath, const FString& ResponseFilePath, const FString& AdditionalArgs)
{
    FString UnrealPakPath = GetUnrealPakPath();
    if (UnrealPakPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak executable not found"));
        return false;
    }

    // Build command line arguments
    FString Arguments = FString::Printf(TEXT("\"%s\" -create=\"%s\" %s"), *PakFilePath, *ResponseFilePath, *AdditionalArgs);

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing UnrealPak: %s %s"), *UnrealPakPath, *Arguments);

    // Execute UnrealPak
    int32 ReturnCode = -1;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(*UnrealPakPath, *Arguments, &ReturnCode, &StdOut, &StdErr);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak completed successfully"));
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak output: %s"), *StdOut);
        }
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak failed with return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak error: %s"), *StdErr);
        }
        if (!StdOut.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("UnrealPak output: %s"), *StdOut);
        }
        return false;
    }
}

TArray<FString> FNeoPakFileCreator::ConvertAssetPathsToFilePaths(const TArray<FString>& AssetPaths)
{
    TArray<FString> FilePaths;
    FString ContentDir = GetContentDirectory();

    UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset paths to file paths. Content directory: %s"), *ContentDir);

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset path: %s"), *AssetPath);

        FString FilePath;
        bool bConversionSuccessful = false;

        // First try our alternative conversion method (better for maps and special cases)
        FString AlternativeFilePath = ConvertAssetPathAlternative(AssetPath);
        if (!AlternativeFilePath.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Alternative conversion successful: %s"), *AlternativeFilePath);
            FilePaths.Add(AlternativeFilePath);
            bConversionSuccessful = true;
        }
        else
        {
            // Fallback to standard conversion
            if (FPackageName::TryConvertLongPackageNameToFilename(AssetPath, FilePath))
            {
                // Convert to absolute path if it's relative
                if (FPaths::IsRelative(FilePath))
                {
                    FilePath = FPaths::ConvertRelativePathToFull(FilePath);
                }

                UE_LOG(LogNeoPakTools, Log, TEXT("  Standard conversion successful: %s"), *FilePath);
                FilePaths.Add(FilePath);
                bConversionSuccessful = true;
            }
        }

        if (!bConversionSuccessful)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("  Failed to convert asset path to file path: %s"), *AssetPath);
            FilePaths.Add(TEXT(""));
        }
    }

    return FilePaths;
}

FString FNeoPakFileCreator::GetContentDirectory()
{
    FString ContentDir = FPaths::ProjectContentDir();
    // Ensure it's an absolute path
    ContentDir = FPaths::ConvertRelativePathToFull(ContentDir);
    // Normalize path separators
    FPaths::NormalizeDirectoryName(ContentDir);
    return ContentDir;
}

bool FNeoPakFileCreator::ValidateAssetPaths(const TArray<FString>& AssetPaths, TArray<FString>& OutMissingAssets)
{
    OutMissingAssets.Empty();

    UE_LOG(LogNeoPakTools, Log, TEXT("Validating %d asset paths"), AssetPaths.Num());

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Validating asset path: %s"), *AssetPath);

        // Check if the asset path is valid format
        if (AssetPath.IsEmpty() || !AssetPath.StartsWith(TEXT("/Game/")))
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("  Invalid asset path format: %s"), *AssetPath);
            OutMissingAssets.Add(AssetPath);
            continue;
        }

        // Try to validate using Asset Registry first (more reliable for UE assets)
        if (ValidateAssetUsingRegistry(AssetPath))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Asset found in registry: %s"), *AssetPath);
            continue;
        }

        // For now, if asset registry doesn't find it, we'll still try to package it
        // UnrealPak will give us the final word on whether the asset exists
        UE_LOG(LogNeoPakTools, Warning, TEXT("  Asset not found in registry, but will attempt to package: %s"), *AssetPath);
    }

    // For now, we'll be more permissive and let UnrealPak handle missing assets
    // This prevents false positives where assets exist but aren't found by our validation
    UE_LOG(LogNeoPakTools, Log, TEXT("Asset validation completed. Found %d potentially missing assets, but proceeding with packaging."), OutMissingAssets.Num());

    // Clear missing assets for now - let UnrealPak be the final judge
    OutMissingAssets.Empty();
    return true;
}

FString FNeoPakFileCreator::GenerateResponseFilePath(const FString& PakFileName)
{
    FString TempDir = FPaths::ProjectIntermediateDir() / TEXT("NeoPakTools");
    FString ResponseFileName = PakFileName + TEXT("_response.txt");
    return TempDir / ResponseFileName;
}

void FNeoPakFileCreator::CleanupTempFiles(const FString& ResponseFilePath)
{
    if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*ResponseFilePath))
    {
        FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*ResponseFilePath);
        UE_LOG(LogNeoPakTools, Log, TEXT("Cleaned up response file: %s"), *ResponseFilePath);
    }
}

bool FNeoPakFileCreator::ValidateAssetUsingRegistry(const FString& AssetPath)
{
    // Use Asset Registry to check if asset exists
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Convert the asset path to FSoftObjectPath for registry lookup
    FSoftObjectPath SoftPath(AssetPath);
    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(SoftPath);

    if (AssetData.IsValid())
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Asset found in registry: %s (Class: %s)"), *AssetPath, *AssetData.AssetClassPath.ToString());
        return true;
    }

    UE_LOG(LogNeoPakTools, Warning, TEXT("Asset not found in registry: %s"), *AssetPath);
    return false;
}

FString FNeoPakFileCreator::ConvertAssetPathAlternative(const FString& AssetPath)
{
    // Handle special cases where standard conversion fails
    FString ContentDir = GetContentDirectory();

    UE_LOG(LogNeoPakTools, Log, TEXT("Alternative conversion for: %s"), *AssetPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Content directory: %s"), *ContentDir);

    // Remove /Game/ prefix and add Content directory
    FString RelativePath = AssetPath;
    if (RelativePath.StartsWith(TEXT("/Game/")))
    {
        RelativePath = RelativePath.RightChop(6); // Remove "/Game/"
        UE_LOG(LogNeoPakTools, Log, TEXT("Removed /Game/ prefix, relative path: %s"), *RelativePath);
    }

    // Handle special case for assets with duplicate names (like Maps/NewMap.NewMap)
    // Remove the duplicate part after the dot
    int32 DotIndex = RelativePath.Find(TEXT("."), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
    if (DotIndex != INDEX_NONE)
    {
        FString BaseName = RelativePath.Left(DotIndex);
        FString Extension = RelativePath.RightChop(DotIndex + 1);

        // Check if the extension matches the base name (like NewMap.NewMap)
        FString BaseFileName = FPaths::GetBaseFilename(BaseName);
        if (Extension == BaseFileName)
        {
            // This is likely a map or similar asset, use just the base path
            RelativePath = BaseName;
            UE_LOG(LogNeoPakTools, Log, TEXT("Detected duplicate name format, using base path: %s"), *RelativePath);
        }
    }

    // Try different file extensions
    TArray<FString> PossibleExtensions = {TEXT(".umap"), TEXT(".uasset"), TEXT(".ubulk")};

    for (const FString& Extension : PossibleExtensions)
    {
        FString TestPath = FPaths::Combine(ContentDir, RelativePath + Extension);
        // Ensure absolute path
        TestPath = FPaths::ConvertRelativePathToFull(TestPath);
        UE_LOG(LogNeoPakTools, Log, TEXT("Testing path: %s"), *TestPath);

        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestPath))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Found alternative file path: %s"), *TestPath);
            return TestPath;
        }
    }

    UE_LOG(LogNeoPakTools, Warning, TEXT("No alternative file path found for: %s"), *AssetPath);
    return TEXT("");
}
