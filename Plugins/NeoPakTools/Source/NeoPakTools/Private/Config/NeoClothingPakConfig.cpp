// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoClothingPakConfig.h"
#include "NeoPakTools.h"
#include "PakManager/NeoPakManager.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoClothingPakConfig, Log, All);

#if WITH_EDITOR
bool UNeoClothingPakConfig::ValidateConfiguration()
{
    // 检查依赖的骨骼资产DataAsset（使用更可靠的方法）
    if (RequiredSkeletonConfig.IsNull())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Required skeleton config is not specified"));
        return false;
    }

    // 检查骨骼配置路径是否有效
    FString SkeletonConfigPath = RequiredSkeletonConfig.GetLongPackageName();
    if (SkeletonConfigPath.IsEmpty())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Required skeleton config path is empty"));
        return false;
    }

    // 检查是否有服装网格（使用更可靠的方法）
    if (ClothingMesh.IsNull())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("No clothing mesh specified"));
        return false;
    }

    // 检查服装网格路径是否有效
    FString ClothingMeshPath = ClothingMesh.GetLongPackageName();
    if (ClothingMeshPath.IsEmpty())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Clothing mesh path is empty"));
        return false;
    }
    
    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }
    
    return true;
}

bool UNeoClothingPakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }

    UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Executing packaging for clothing config: %s"), *ConfigName);
    UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Clothing Mesh: %s"), *ClothingMesh.ToString());
    UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Clothing Type: %d"), (int32)ClothingType);
    UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Required Skeleton Config: %s"), *RequiredSkeletonConfig.ToString());
    UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());

    // 使用PAK管理器执行打包
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        UE_LOG(LogNeoClothingPakConfig, Error, TEXT("Failed to get PAK Manager instance"));
        return false;
    }

    return PakManager->PackageFromConfig(this);
}

TArray<FSoftObjectPath> UNeoClothingPakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;

    if (!ClothingMesh.IsNull())
    {
        Assets.Add(ClothingMesh.ToSoftObjectPath());
        UE_LOG(LogNeoClothingPakConfig, Log, TEXT("Added clothing mesh to package: %s"), *ClothingMesh.ToString());
    }
    else
    {
        UE_LOG(LogNeoClothingPakConfig, Warning, TEXT("Clothing mesh is null or not set"));
    }

    return Assets;
}
#endif
