// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoAnimMontagePakConfig.h"
#include "NeoPakTools.h"
#include "PakManager/NeoPakManager.h"
#include "Animation/Skeleton.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoAnimMontagePakConfig, Log, All);

#if WITH_EDITOR
bool UNeoAnimMontagePakConfig::ValidateConfiguration()
{
    // 检查依赖的骨骼资产DataAsset（使用更可靠的方法）
    if (RequiredSkeletonConfig.IsNull())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Required skeleton config is not specified"));
        return false;
    }

    // 检查骨骼配置路径是否有效
    FString SkeletonConfigPath = RequiredSkeletonConfig.GetLongPackageName();
    if (SkeletonConfigPath.IsEmpty())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Required skeleton config path is empty"));
        return false;
    }

    // 检查是否有动画蒙太奇（使用更可靠的方法）
    if (AnimationMontage.IsNull())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("No animation montage specified"));
        return false;
    }

    // 检查动画蒙太奇路径是否有效
    FString AnimMontagePath = AnimationMontage.GetLongPackageName();
    if (AnimMontagePath.IsEmpty())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Animation montage path is empty"));
        return false;
    }

    // 验证动画蒙太奇与骨骼的兼容性
    if (!IsAnimMontageCompatibleWithSkeletonConfig(AnimationMontage, RequiredSkeletonConfig))
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Animation montage %s is not compatible with skeleton config %s"),
               *AnimationMontage.ToString(), *RequiredSkeletonConfig.ToString());
        return false;
    }

    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }

    return true;
}

bool UNeoAnimMontagePakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }

    UE_LOG(LogNeoAnimMontagePakConfig, Log, TEXT("Executing packaging for animation montage config: %s"), *ConfigName);
    UE_LOG(LogNeoAnimMontagePakConfig, Log, TEXT("Animation Montage: %s"), *AnimationMontage.ToString());
    UE_LOG(LogNeoAnimMontagePakConfig, Log, TEXT("Required Skeleton Config: %s"), *RequiredSkeletonConfig.ToString());
    UE_LOG(LogNeoAnimMontagePakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());

    // 使用PAK管理器执行打包
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Error, TEXT("Failed to get PAK Manager instance"));
        return false;
    }

    return PakManager->PackageFromConfig(this);
}

TArray<FSoftObjectPath> UNeoAnimMontagePakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;

    if (!AnimationMontage.IsNull())
    {
        Assets.Add(AnimationMontage.ToSoftObjectPath());
        UE_LOG(LogNeoAnimMontagePakConfig, Log, TEXT("Added animation montage to package: %s"), *AnimationMontage.ToString());
    }
    else
    {
        UE_LOG(LogNeoAnimMontagePakConfig, Warning, TEXT("Animation montage is null or not set"));
    }

    return Assets;
}

bool UNeoAnimMontagePakConfig::IsAnimMontageCompatibleWithSkeletonConfig(const TSoftObjectPtr<UAnimMontage>& AnimMontage, const TSoftObjectPtr<UNeoSkeletonPakConfig>& SkeletonConfig) const
{
    // 加载动画蒙太奇和骨骼配置
    if (UAnimMontage* LoadedAnimMontage = AnimMontage.LoadSynchronous())
    {
        if (UNeoSkeletonPakConfig* LoadedSkeletonConfig = SkeletonConfig.LoadSynchronous())
        {
            if (USkeleton* LoadedSkeleton = LoadedSkeletonConfig->SkeletonAsset.LoadSynchronous())
            {
                // 检查动画蒙太奇的骨骼是否与配置中的骨骼兼容
                return LoadedAnimMontage->GetSkeleton() == LoadedSkeleton || 
                       LoadedAnimMontage->GetSkeleton()->IsCompatible(LoadedSkeleton);
            }
        }
    }
    
    return false;
}
#endif
