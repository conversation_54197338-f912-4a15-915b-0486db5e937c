// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/NeoAnimSequencePakConfig.h"
#include "NeoPakTools.h"
#include "PakManager/NeoPakManager.h"
#include "Animation/Skeleton.h"

DEFINE_LOG_CATEGORY_STATIC(LogNeoAnimSequencePakConfig, Log, All);

#if WITH_EDITOR
bool UNeoAnimSequencePakConfig::ValidateConfiguration()
{
    // 检查依赖的骨骼资产DataAsset（使用更可靠的方法）
    if (RequiredSkeletonConfig.IsNull())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Required skeleton config is not specified"));
        return false;
    }

    // 检查骨骼配置路径是否有效
    FString SkeletonConfigPath = RequiredSkeletonConfig.GetLongPackageName();
    if (SkeletonConfigPath.IsEmpty())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Required skeleton config path is empty"));
        return false;
    }

    // 检查是否有动画序列（使用更可靠的方法）
    if (AnimationSequence.IsNull())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("No animation sequence specified"));
        return false;
    }

    // 检查动画序列路径是否有效
    FString AnimSequencePath = AnimationSequence.GetLongPackageName();
    if (AnimSequencePath.IsEmpty())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Animation sequence path is empty"));
        return false;
    }

    // 验证动画序列与骨骼的兼容性
    if (!IsAnimationCompatibleWithSkeletonConfig(AnimationSequence, RequiredSkeletonConfig))
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Animation sequence %s is not compatible with skeleton config %s"),
               *AnimationSequence.ToString(), *RequiredSkeletonConfig.ToString());
        return false;
    }

    // 检查输出文件名
    if (OutputPakFileName.IsEmpty())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Output PAK file name is empty"));
        return false;
    }

    return true;
}

bool UNeoAnimSequencePakConfig::ExecutePackaging()
{
    // 首先验证配置
    if (!ValidateConfiguration())
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Configuration validation failed for: %s"), *ConfigName);
        return false;
    }

    UE_LOG(LogNeoAnimSequencePakConfig, Log, TEXT("Executing packaging for animation sequence config: %s"), *ConfigName);
    UE_LOG(LogNeoAnimSequencePakConfig, Log, TEXT("Animation Sequence: %s"), *AnimationSequence.ToString());
    UE_LOG(LogNeoAnimSequencePakConfig, Log, TEXT("Required Skeleton Config: %s"), *RequiredSkeletonConfig.ToString());
    UE_LOG(LogNeoAnimSequencePakConfig, Log, TEXT("Output Path: %s"), *GetFullOutputPath());

    // 使用PAK管理器执行打包
    UNeoPakManager* PakManager = UNeoPakManager::GetInstance();
    if (!PakManager)
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Error, TEXT("Failed to get PAK Manager instance"));
        return false;
    }

    return PakManager->PackageFromConfig(this);
}

TArray<FSoftObjectPath> UNeoAnimSequencePakConfig::GetAssetsToPackage() const
{
    TArray<FSoftObjectPath> Assets;

    if (!AnimationSequence.IsNull())
    {
        Assets.Add(AnimationSequence.ToSoftObjectPath());
        UE_LOG(LogNeoAnimSequencePakConfig, Log, TEXT("Added animation sequence to package: %s"), *AnimationSequence.ToString());
    }
    else
    {
        UE_LOG(LogNeoAnimSequencePakConfig, Warning, TEXT("Animation sequence is null or not set"));
    }

    return Assets;
}

bool UNeoAnimSequencePakConfig::IsAnimationCompatibleWithSkeletonConfig(const TSoftObjectPtr<UAnimSequence>& AnimSeq, const TSoftObjectPtr<UNeoSkeletonPakConfig>& SkeletonConfig) const
{
    // 加载动画序列和骨骼配置
    if (UAnimSequence* LoadedAnimSeq = AnimSeq.LoadSynchronous())
    {
        if (UNeoSkeletonPakConfig* LoadedSkeletonConfig = SkeletonConfig.LoadSynchronous())
        {
            if (USkeleton* LoadedSkeleton = LoadedSkeletonConfig->SkeletonAsset.LoadSynchronous())
            {
                // 检查动画序列的骨骼是否与配置中的骨骼兼容
                return LoadedAnimSeq->GetSkeleton() == LoadedSkeleton || 
                       LoadedAnimSeq->GetSkeleton()->IsCompatible(LoadedSkeleton);
            }
        }
    }
    
    return false;
}
#endif
