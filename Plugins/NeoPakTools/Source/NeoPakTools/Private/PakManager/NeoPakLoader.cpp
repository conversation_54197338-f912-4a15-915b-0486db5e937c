// Copyright Epic Games, Inc. All Rights Reserved.

#include "PakManager/NeoPakLoader.h"
#include "NeoPakTools.h"
#include "HAL/PlatformFilemanager.h"
#include "IPlatformFilePak.h"
#include "Misc/DateTime.h"
#include "Engine/AssetManager.h"
#include "AssetRegistry/AssetRegistryModule.h"

DEFINE_LOG_CATEGORY(LogNeoPakLoader);

// FNeoPakLoadTask Implementation
FNeoPakLoadTask::FNeoPakLoadTask(const FString& InPakPath, bool bInPreloadDependencies)
    : PakPath(InPakPath)
    , bPreloadDependencies(bInPreloadDependencies)
{
    LoadResult.PakFilePath = PakPath;
    LoadResult.LoadStatus = EPakLoadStatus::Loading;
}

void FNeoPakLoadTask::DoWork()
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Starting async load for PAK: %s"), *PakPath);
    
    double StartTime = FPlatformTime::Seconds();
    
    LoadResult.bSuccess = LoadPakFile();
    
    if (LoadResult.bSuccess)
    {
        LoadResult.LoadStatus = EPakLoadStatus::Loaded;
        
        if (bPreloadDependencies)
        {
            PreloadDependencies();
        }
    }
    else
    {
        LoadResult.LoadStatus = EPakLoadStatus::Failed;
    }
    
    double EndTime = FPlatformTime::Seconds();
    LoadResult.LoadTimeMs = (EndTime - StartTime) * 1000.0f;
    
    UE_LOG(LogNeoPakLoader, Log, TEXT("Async load completed for PAK: %s, Success: %s, Time: %.2fms"), 
           *PakPath, LoadResult.bSuccess ? TEXT("true") : TEXT("false"), LoadResult.LoadTimeMs);
}

bool FNeoPakLoadTask::LoadPakFile()
{
    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakPath))
    {
        LoadResult.ErrorMessage = FString::Printf(TEXT("PAK file does not exist: %s"), *PakPath);
        UE_LOG(LogNeoPakLoader, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    // 获取PAK平台文件
    FPakPlatformFile* PakPlatformFile = nullptr;
    IPlatformFile* LowerLevel = &FPlatformFileManager::Get().GetPlatformFile();
    
    // 查找PAK平台文件层
    while (LowerLevel)
    {
        PakPlatformFile = static_cast<FPakPlatformFile*>(LowerLevel->GetLowerLevel());
        if (PakPlatformFile && PakPlatformFile->GetTypeName() == TEXT("PakFile"))
        {
            break;
        }
        LowerLevel = LowerLevel->GetLowerLevel();
    }

    if (!PakPlatformFile)
    {
        LoadResult.ErrorMessage = TEXT("Failed to find PAK platform file");
        UE_LOG(LogNeoPakLoader, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    // 尝试挂载PAK文件
    bool bMounted = PakPlatformFile->Mount(*PakPath, 0, nullptr);
    if (!bMounted)
    {
        LoadResult.ErrorMessage = FString::Printf(TEXT("Failed to mount PAK file: %s"), *PakPath);
        UE_LOG(LogNeoPakLoader, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    UE_LOG(LogNeoPakLoader, Log, TEXT("Successfully mounted PAK file: %s"), *PakPath);
    return true;
}

void FNeoPakLoadTask::PreloadDependencies()
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Preloading dependencies for PAK: %s"), *PakPath);
    
    // 这里可以实现具体的依赖预加载逻辑
    // 例如：扫描PAK中的资产，加载关键依赖等
    
    // 暂时只是记录日志
    LoadResult.LoadedAssetCount = 0; // 实际实现时应该返回真实的加载数量
}

// UNeoPakLoader Implementation
UNeoPakLoader::UNeoPakLoader()
    : NextTaskHandle(1)
    , TotalLoadedCount(0)
    , TotalFailedCount(0)
    , TotalLoadTime(0.0f)
{
}

int32 UNeoPakLoader::LoadPakAsync(const FString& PakFilePath, bool bPreloadDependencies)
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Starting async load for PAK: %s"), *PakFilePath);
    
    // 检查是否已经在加载或已加载
    EPakLoadStatus CurrentStatus = GetLoadStatus(PakFilePath);
    if (CurrentStatus == EPakLoadStatus::Loading || CurrentStatus == EPakLoadStatus::Loaded)
    {
        UE_LOG(LogNeoPakLoader, Warning, TEXT("PAK is already loading or loaded: %s"), *PakFilePath);
        return -1;
    }

    // 更新状态
    UpdatePakStatus(PakFilePath, EPakLoadStatus::Loading);

    // 创建异步任务
    int32 TaskHandle = GenerateTaskHandle();
    TSharedPtr<FAsyncTask<FNeoPakLoadTask>> AsyncTask = 
        MakeShared<FAsyncTask<FNeoPakLoadTask>>(PakFilePath, bPreloadDependencies);
    
    AsyncTasks.Add(TaskHandle, AsyncTask);
    
    // 启动任务
    AsyncTask->StartBackgroundTask();
    
    UE_LOG(LogNeoPakLoader, Log, TEXT("Async load task started with handle: %d"), TaskHandle);
    return TaskHandle;
}

FNeoPakLoadResult UNeoPakLoader::LoadPakSync(const FString& PakFilePath, bool bPreloadDependencies)
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Starting sync load for PAK: %s"), *PakFilePath);
    
    // 检查是否已经加载
    EPakLoadStatus CurrentStatus = GetLoadStatus(PakFilePath);
    if (CurrentStatus == EPakLoadStatus::Loaded)
    {
        FNeoPakLoadResult Result;
        Result.bSuccess = true;
        Result.PakFilePath = PakFilePath;
        Result.LoadStatus = EPakLoadStatus::Loaded;
        UE_LOG(LogNeoPakLoader, Log, TEXT("PAK already loaded: %s"), *PakFilePath);
        return Result;
    }

    // 更新状态
    UpdatePakStatus(PakFilePath, EPakLoadStatus::Loading);

    // 执行同步加载
    FNeoPakLoadResult Result = LoadPakInternal(PakFilePath, bPreloadDependencies);
    
    // 更新状态
    UpdatePakStatus(PakFilePath, Result.LoadStatus);
    
    // 更新统计
    if (Result.bSuccess)
    {
        TotalLoadedCount++;
    }
    else
    {
        TotalFailedCount++;
    }
    TotalLoadTime += Result.LoadTimeMs;

    return Result;
}

bool UNeoPakLoader::UnloadPak(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Unloading PAK: %s"), *PakFilePath);
    
    // 更新状态
    UpdatePakStatus(PakFilePath, EPakLoadStatus::Unloading);

    // 获取PAK平台文件并卸载
    FPakPlatformFile* PakPlatformFile = nullptr;
    IPlatformFile* LowerLevel = &FPlatformFileManager::Get().GetPlatformFile();
    
    while (LowerLevel)
    {
        PakPlatformFile = static_cast<FPakPlatformFile*>(LowerLevel->GetLowerLevel());
        if (PakPlatformFile && PakPlatformFile->GetTypeName() == TEXT("PakFile"))
        {
            break;
        }
        LowerLevel = LowerLevel->GetLowerLevel();
    }

    if (PakPlatformFile)
    {
        bool bUnmounted = PakPlatformFile->Unmount(*PakFilePath);
        if (bUnmounted)
        {
            PakStatusMap.Remove(PakFilePath);
            UE_LOG(LogNeoPakLoader, Log, TEXT("Successfully unloaded PAK: %s"), *PakFilePath);
            return true;
        }
        else
        {
            UpdatePakStatus(PakFilePath, EPakLoadStatus::Failed);
            UE_LOG(LogNeoPakLoader, Error, TEXT("Failed to unload PAK: %s"), *PakFilePath);
            return false;
        }
    }

    UE_LOG(LogNeoPakLoader, Error, TEXT("PAK platform file not found for unloading: %s"), *PakFilePath);
    return false;
}

EPakLoadStatus UNeoPakLoader::GetLoadStatus(const FString& PakFilePath) const
{
    if (const EPakLoadStatus* Status = PakStatusMap.Find(PakFilePath))
    {
        return *Status;
    }
    return EPakLoadStatus::NotLoaded;
}

bool UNeoPakLoader::GetAsyncLoadResult(int32 TaskHandle, FNeoPakLoadResult& OutResult)
{
    CleanupCompletedTasks();
    
    if (TSharedPtr<FAsyncTask<FNeoPakLoadTask>>* TaskPtr = AsyncTasks.Find(TaskHandle))
    {
        TSharedPtr<FAsyncTask<FNeoPakLoadTask>> Task = *TaskPtr;
        if (Task->IsDone())
        {
            OutResult = Task->GetTask().GetResult();
            
            // 更新状态和统计
            UpdatePakStatus(OutResult.PakFilePath, OutResult.LoadStatus);
            if (OutResult.bSuccess)
            {
                TotalLoadedCount++;
            }
            else
            {
                TotalFailedCount++;
            }
            TotalLoadTime += OutResult.LoadTimeMs;
            
            // 移除完成的任务
            AsyncTasks.Remove(TaskHandle);
            return true;
        }
    }
    
    return false;
}

bool UNeoPakLoader::CancelAsyncLoad(int32 TaskHandle)
{
    if (TSharedPtr<FAsyncTask<FNeoPakLoadTask>>* TaskPtr = AsyncTasks.Find(TaskHandle))
    {
        // 注意：FAsyncTask不支持取消，这里只是移除引用
        AsyncTasks.Remove(TaskHandle);
        UE_LOG(LogNeoPakLoader, Log, TEXT("Async load task handle removed: %d"), TaskHandle);
        return true;
    }
    
    return false;
}

TArray<FString> UNeoPakLoader::GetLoadedPakFiles() const
{
    TArray<FString> LoadedFiles;
    
    for (const auto& Pair : PakStatusMap)
    {
        if (Pair.Value == EPakLoadStatus::Loaded)
        {
            LoadedFiles.Add(Pair.Key);
        }
    }
    
    return LoadedFiles;
}

bool UNeoPakLoader::PreloadDependencies(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakLoader, Log, TEXT("Preloading dependencies for PAK: %s"), *PakFilePath);
    
    // 检查PAK是否已加载
    if (GetLoadStatus(PakFilePath) != EPakLoadStatus::Loaded)
    {
        UE_LOG(LogNeoPakLoader, Warning, TEXT("PAK is not loaded, cannot preload dependencies: %s"), *PakFilePath);
        return false;
    }

    // 这里可以实现具体的依赖预加载逻辑
    // 暂时返回true
    return true;
}

void UNeoPakLoader::ClearAllLoadStates()
{
    PakStatusMap.Empty();
    AsyncTasks.Empty();
    TotalLoadedCount = 0;
    TotalFailedCount = 0;
    TotalLoadTime = 0.0f;
    NextTaskHandle = 1;
    
    UE_LOG(LogNeoPakLoader, Log, TEXT("All load states cleared"));
}

void UNeoPakLoader::GetLoadStatistics(int32& OutTotalLoaded, int32& OutTotalFailed, float& OutAverageLoadTime) const
{
    OutTotalLoaded = TotalLoadedCount;
    OutTotalFailed = TotalFailedCount;
    
    int32 TotalAttempts = TotalLoadedCount + TotalFailedCount;
    OutAverageLoadTime = (TotalAttempts > 0) ? (TotalLoadTime / TotalAttempts) : 0.0f;
}

int32 UNeoPakLoader::GenerateTaskHandle()
{
    return NextTaskHandle++;
}

void UNeoPakLoader::UpdatePakStatus(const FString& PakFilePath, EPakLoadStatus NewStatus)
{
    PakStatusMap.Add(PakFilePath, NewStatus);
}

void UNeoPakLoader::CleanupCompletedTasks()
{
    TArray<int32> CompletedTasks;
    
    for (const auto& Pair : AsyncTasks)
    {
        if (Pair.Value->IsDone())
        {
            CompletedTasks.Add(Pair.Key);
        }
    }
    
    for (int32 TaskHandle : CompletedTasks)
    {
        AsyncTasks.Remove(TaskHandle);
    }
}

FNeoPakLoadResult UNeoPakLoader::LoadPakInternal(const FString& PakFilePath, bool bPreloadDependencies)
{
    FNeoPakLoadTask Task(PakFilePath, bPreloadDependencies);
    Task.DoWork();
    return Task.GetResult();
}
