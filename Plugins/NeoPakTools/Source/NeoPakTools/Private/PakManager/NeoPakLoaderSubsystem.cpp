// Copyright Epic Games, Inc. All Rights Reserved.

#include "PakManager/NeoPakLoaderSubsystem.h"
#include "NeoPakTools.h"
#include "HAL/PlatformFilemanager.h"
#include "IPlatformFilePak.h"
#include "Misc/DateTime.h"
#include "Engine/AssetManager.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

DEFINE_LOG_CATEGORY(LogNeoPakLoaderSubsystem);

// FNeoPakLoadTask Implementation
FNeoPakLoadTask::FNeoPakLoadTask(const FString& InPakPath, bool bInPreloadDependencies)
    : PakPath(InPakPath)
    , bPreloadDependencies(bInPreloadDependencies)
{
    LoadResult.PakFilePath = PakPath;
    LoadResult.LoadStatus = ENeoPakLoadStatus::Loading;
}

void FNeoPakLoadTask::DoWork()
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Starting async load for PAK: %s"), *PakPath);
    
    double StartTime = FPlatformTime::Seconds();
    
    LoadResult.bSuccess = LoadPakFile();
    
    if (LoadResult.bSuccess)
    {
        LoadResult.LoadStatus = ENeoPakLoadStatus::Loaded;
        
        if (bPreloadDependencies)
        {
            PreloadDependencies();
        }
    }
    else
    {
        LoadResult.LoadStatus = ENeoPakLoadStatus::Failed;
    }
    
    double EndTime = FPlatformTime::Seconds();
    LoadResult.LoadTimeMs = (EndTime - StartTime) * 1000.0f;
    
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Async load completed for PAK: %s, Success: %s, Time: %.2fms"), 
           *PakPath, LoadResult.bSuccess ? TEXT("true") : TEXT("false"), LoadResult.LoadTimeMs);
}

bool FNeoPakLoadTask::LoadPakFile()
{
    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakPath))
    {
        LoadResult.ErrorMessage = FString::Printf(TEXT("PAK file does not exist: %s"), *PakPath);
        UE_LOG(LogNeoPakLoaderSubsystem, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    // 在UE5.6中，使用正确的方法获取PAK平台文件
    FPakPlatformFile* PakPlatformFile = nullptr;

    // 方法1：直接从平台文件管理器获取
    IPlatformFile* CurrentPlatformFile = &FPlatformFileManager::Get().GetPlatformFile();

    // 遍历平台文件链，查找PAK平台文件
    while (CurrentPlatformFile)
    {
        if (FCString::Strcmp(CurrentPlatformFile->GetTypeName(), TEXT("PakFile")) == 0)
        {
            PakPlatformFile = static_cast<FPakPlatformFile*>(CurrentPlatformFile);
            break;
        }
        CurrentPlatformFile = CurrentPlatformFile->GetLowerLevel();
    }

    // 如果没有找到，尝试创建或获取PAK平台文件
    if (!PakPlatformFile)
    {
        // 尝试通过FPlatformFileManager获取PAK文件接口
        IPlatformFile* PlatformFile = FPlatformFileManager::Get().FindPlatformFile(TEXT("PakFile"));
        if (PlatformFile)
        {
            PakPlatformFile = static_cast<FPakPlatformFile*>(PlatformFile);
        }
    }

    if (!PakPlatformFile)
    {
        LoadResult.ErrorMessage = TEXT("Failed to find PAK platform file. PAK system may not be initialized.");
        UE_LOG(LogNeoPakLoaderSubsystem, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    // 尝试挂载PAK文件
    bool bMounted = PakPlatformFile->Mount(*PakPath, 0, nullptr);
    if (!bMounted)
    {
        LoadResult.ErrorMessage = FString::Printf(TEXT("Failed to mount PAK file: %s"), *PakPath);
        UE_LOG(LogNeoPakLoaderSubsystem, Error, TEXT("%s"), *LoadResult.ErrorMessage);
        return false;
    }

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Successfully mounted PAK file: %s"), *PakPath);
    return true;
}

void FNeoPakLoadTask::PreloadDependencies()
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Preloading dependencies for PAK: %s"), *PakPath);
    
    // 这里可以实现具体的依赖预加载逻辑
    // 例如：扫描PAK中的资产，加载关键依赖等
    
    // 暂时只是记录日志
    LoadResult.LoadedAssetCount = 0; // 实际实现时应该返回真实的加载数量
}

// UNeoPakLoaderSubsystem Implementation
void UNeoPakLoaderSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    NextTaskHandle = 1;
    TotalLoadedCount = 0;
    TotalFailedCount = 0;
    TotalLoadTime = 0.0f;

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("NeoPakLoaderSubsystem initialized"));
}

void UNeoPakLoaderSubsystem::Deinitialize()
{
    // 清理所有异步任务
    AsyncTasks.Empty();

    // 清理状态
    ClearAllLoadStates();

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("NeoPakLoaderSubsystem deinitialized"));

    Super::Deinitialize();
}

UNeoPakLoaderSubsystem* UNeoPakLoaderSubsystem::Get()
{
    if (GEngine)
    {
        return GEngine->GetEngineSubsystem<UNeoPakLoaderSubsystem>();
    }
    return nullptr;
}

int32 UNeoPakLoaderSubsystem::LoadPakAsync(const FString& PakFilePath, bool bPreloadDependencies)
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Starting async load for PAK: %s"), *PakFilePath);
    
    // 检查是否已经在加载或已加载
    ENeoPakLoadStatus CurrentStatus = GetLoadStatus(PakFilePath);
    if (CurrentStatus == ENeoPakLoadStatus::Loading || CurrentStatus == ENeoPakLoadStatus::Loaded)
    {
        UE_LOG(LogNeoPakLoaderSubsystem, Warning, TEXT("PAK is already loading or loaded: %s"), *PakFilePath);
        return -1;
    }

    // 更新状态
    UpdatePakStatus(PakFilePath, ENeoPakLoadStatus::Loading);

    // 创建异步任务
    int32 TaskHandle = GenerateTaskHandle();
    TSharedPtr<FAsyncTask<FNeoPakLoadTask>> AsyncTask = 
        MakeShared<FAsyncTask<FNeoPakLoadTask>>(PakFilePath, bPreloadDependencies);
    
    AsyncTasks.Add(TaskHandle, AsyncTask);
    
    // 启动任务
    AsyncTask->StartBackgroundTask();
    
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Async load task started with handle: %d"), TaskHandle);
    return TaskHandle;
}

FNeoPakLoadResult UNeoPakLoaderSubsystem::LoadPakSync(const FString& PakFilePath, bool bPreloadDependencies)
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Starting sync load for PAK: %s"), *PakFilePath);
    
    // 检查是否已经加载
    ENeoPakLoadStatus CurrentStatus = GetLoadStatus(PakFilePath);
    if (CurrentStatus == ENeoPakLoadStatus::Loaded)
    {
        FNeoPakLoadResult Result;
        Result.bSuccess = true;
        Result.PakFilePath = PakFilePath;
        Result.LoadStatus = ENeoPakLoadStatus::Loaded;
        UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("PAK already loaded: %s"), *PakFilePath);
        return Result;
    }

    // 更新状态
    UpdatePakStatus(PakFilePath, ENeoPakLoadStatus::Loading);

    // 执行同步加载
    FNeoPakLoadResult Result = LoadPakInternal(PakFilePath, bPreloadDependencies);
    
    // 更新状态
    UpdatePakStatus(PakFilePath, Result.LoadStatus);
    
    // 更新统计
    if (Result.bSuccess)
    {
        TotalLoadedCount++;
    }
    else
    {
        TotalFailedCount++;
    }
    TotalLoadTime += Result.LoadTimeMs;

    return Result;
}

bool UNeoPakLoaderSubsystem::UnloadPak(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Unloading PAK: %s"), *PakFilePath);
    
    // 更新状态
    UpdatePakStatus(PakFilePath, ENeoPakLoadStatus::Unloading);

    // 获取PAK平台文件并卸载
    FPakPlatformFile* PakPlatformFile = nullptr;
    IPlatformFile* LowerLevel = &FPlatformFileManager::Get().GetPlatformFile();
    
    while (LowerLevel)
    {
        PakPlatformFile = static_cast<FPakPlatformFile*>(LowerLevel->GetLowerLevel());
        if (PakPlatformFile && FCString::Strcmp(PakPlatformFile->GetTypeName(), TEXT("PakFile")) == 0)
        {
            break;
        }
        LowerLevel = LowerLevel->GetLowerLevel();
    }

    if (PakPlatformFile)
    {
        bool bUnmounted = PakPlatformFile->Unmount(*PakFilePath);
        if (bUnmounted)
        {
            PakStatusMap.Remove(PakFilePath);
            UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Successfully unloaded PAK: %s"), *PakFilePath);
            return true;
        }
        else
        {
            UpdatePakStatus(PakFilePath, ENeoPakLoadStatus::Failed);
            UE_LOG(LogNeoPakLoaderSubsystem, Error, TEXT("Failed to unload PAK: %s"), *PakFilePath);
            return false;
        }
    }

    UE_LOG(LogNeoPakLoaderSubsystem, Error, TEXT("PAK platform file not found for unloading: %s"), *PakFilePath);
    return false;
}

ENeoPakLoadStatus UNeoPakLoaderSubsystem::GetLoadStatus(const FString& PakFilePath) const
{
    if (const ENeoPakLoadStatus* Status = PakStatusMap.Find(PakFilePath))
    {
        return *Status;
    }
    return ENeoPakLoadStatus::NotLoaded;
}

bool UNeoPakLoaderSubsystem::GetAsyncLoadResult(int32 TaskHandle, FNeoPakLoadResult& OutResult)
{
    CleanupCompletedTasks();
    
    if (TSharedPtr<FAsyncTask<FNeoPakLoadTask>>* TaskPtr = AsyncTasks.Find(TaskHandle))
    {
        TSharedPtr<FAsyncTask<FNeoPakLoadTask>> Task = *TaskPtr;
        if (Task->IsDone())
        {
            OutResult = Task->GetTask().GetResult();
            
            // 更新状态和统计
            UpdatePakStatus(OutResult.PakFilePath, OutResult.LoadStatus);
            if (OutResult.bSuccess)
            {
                TotalLoadedCount++;
            }
            else
            {
                TotalFailedCount++;
            }
            TotalLoadTime += OutResult.LoadTimeMs;
            
            // 移除完成的任务
            AsyncTasks.Remove(TaskHandle);
            return true;
        }
    }
    
    return false;
}

bool UNeoPakLoaderSubsystem::CancelAsyncLoad(int32 TaskHandle)
{
    if (TSharedPtr<FAsyncTask<FNeoPakLoadTask>>* TaskPtr = AsyncTasks.Find(TaskHandle))
    {
        // 注意：FAsyncTask不支持取消，这里只是移除引用
        AsyncTasks.Remove(TaskHandle);
        UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Async load task handle removed: %d"), TaskHandle);
        return true;
    }

    return false;
}

TArray<FString> UNeoPakLoaderSubsystem::GetLoadedPakFiles() const
{
    TArray<FString> LoadedFiles;

    for (const auto& Pair : PakStatusMap)
    {
        if (Pair.Value == ENeoPakLoadStatus::Loaded)
        {
            LoadedFiles.Add(Pair.Key);
        }
    }

    return LoadedFiles;
}

bool UNeoPakLoaderSubsystem::PreloadDependencies(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Preloading dependencies for PAK: %s"), *PakFilePath);

    // 检查PAK是否已加载
    if (GetLoadStatus(PakFilePath) != ENeoPakLoadStatus::Loaded)
    {
        UE_LOG(LogNeoPakLoaderSubsystem, Warning, TEXT("PAK is not loaded, cannot preload dependencies: %s"), *PakFilePath);
        return false;
    }

    // 这里可以实现具体的依赖预加载逻辑
    // 暂时返回true
    return true;
}

void UNeoPakLoaderSubsystem::ClearAllLoadStates()
{
    PakStatusMap.Empty();
    AsyncTasks.Empty();
    TotalLoadedCount = 0;
    TotalFailedCount = 0;
    TotalLoadTime = 0.0f;
    NextTaskHandle = 1;

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("All load states cleared"));
}

void UNeoPakLoaderSubsystem::GetLoadStatistics(int32& OutTotalLoaded, int32& OutTotalFailed, float& OutAverageLoadTime) const
{
    OutTotalLoaded = TotalLoadedCount;
    OutTotalFailed = TotalFailedCount;

    int32 TotalAttempts = TotalLoadedCount + TotalFailedCount;
    OutAverageLoadTime = (TotalAttempts > 0) ? (TotalLoadTime / TotalAttempts) : 0.0f;
}

TArray<int32> UNeoPakLoaderSubsystem::LoadPaksBatch(const TArray<FString>& PakFilePaths, bool bPreloadDependencies)
{
    TArray<int32> TaskHandles;

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Starting batch load for %d PAK files"), PakFilePaths.Num());

    for (const FString& PakFilePath : PakFilePaths)
    {
        int32 TaskHandle = LoadPakAsync(PakFilePath, bPreloadDependencies);
        if (TaskHandle != -1)
        {
            TaskHandles.Add(TaskHandle);
        }
    }

    UE_LOG(LogNeoPakLoaderSubsystem, Log, TEXT("Batch load started %d tasks"), TaskHandles.Num());
    return TaskHandles;
}

bool UNeoPakLoaderSubsystem::HasActiveLoadTasks() const
{
    for (const auto& Pair : AsyncTasks)
    {
        if (!Pair.Value->IsDone())
        {
            return true;
        }
    }
    return false;
}

int32 UNeoPakLoaderSubsystem::GenerateTaskHandle()
{
    return NextTaskHandle++;
}

void UNeoPakLoaderSubsystem::UpdatePakStatus(const FString& PakFilePath, ENeoPakLoadStatus NewStatus)
{
    PakStatusMap.Add(PakFilePath, NewStatus);
}

void UNeoPakLoaderSubsystem::CleanupCompletedTasks()
{
    TArray<int32> CompletedTasks;

    for (const auto& Pair : AsyncTasks)
    {
        if (Pair.Value->IsDone())
        {
            CompletedTasks.Add(Pair.Key);
        }
    }

    for (int32 TaskHandle : CompletedTasks)
    {
        AsyncTasks.Remove(TaskHandle);
    }
}

FNeoPakLoadResult UNeoPakLoaderSubsystem::LoadPakInternal(const FString& PakFilePath, bool bPreloadDependencies)
{
    FNeoPakLoadTask Task(PakFilePath, bPreloadDependencies);
    Task.DoWork();
    return Task.GetResult();
}

void UNeoPakLoaderSubsystem::PeriodicCleanup()
{
    CleanupCompletedTasks();
}
