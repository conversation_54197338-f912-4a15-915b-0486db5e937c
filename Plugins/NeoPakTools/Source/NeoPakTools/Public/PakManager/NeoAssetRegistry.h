// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "UObject/SoftObjectPath.h"
#include "NeoAssetRegistry.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNeoAssetRegistry, Log, All);

/**
 * 资产信息结构体
 */
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoAssetInfo
{
    GENERATED_BODY()

    // 资产路径
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    FSoftObjectPath AssetPath;

    // 所属PAK文件
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    FString PakFilePath;

    // 资产类型
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    FString AssetClass;

    // 资产大小（字节）
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    int64 AssetSize = 0;

    // 是否已加载
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    bool bIsLoaded = false;

    // 加载时间戳
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    FDateTime LoadTime;

    // 引用计数
    UPROPERTY(BlueprintReadOnly, Category = "Asset Info")
    int32 ReferenceCount = 0;

    FNeoAssetInfo()
    {
        AssetSize = 0;
        bIsLoaded = false;
        ReferenceCount = 0;
        LoadTime = FDateTime::MinValue();
    }

    FNeoAssetInfo(const FSoftObjectPath& InAssetPath, const FString& InPakFilePath, const FString& InAssetClass)
        : AssetPath(InAssetPath)
        , PakFilePath(InPakFilePath)
        , AssetClass(InAssetClass)
        , AssetSize(0)
        , bIsLoaded(false)
        , ReferenceCount(0)
        , LoadTime(FDateTime::MinValue())
    {
    }
};

/**
 * PAK信息结构体
 */
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakInfo
{
    GENERATED_BODY()

    // PAK文件路径
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    FString PakFilePath;

    // PAK文件大小
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    int64 PakFileSize = 0;

    // 包含的资产数量
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    int32 AssetCount = 0;

    // 已加载的资产数量
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    int32 LoadedAssetCount = 0;

    // PAK挂载时间
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    FDateTime MountTime;

    // 是否已挂载
    UPROPERTY(BlueprintReadOnly, Category = "PAK Info")
    bool bIsMounted = false;

    FNeoPakInfo()
    {
        PakFileSize = 0;
        AssetCount = 0;
        LoadedAssetCount = 0;
        bIsMounted = false;
        MountTime = FDateTime::MinValue();
    }

    FNeoPakInfo(const FString& InPakFilePath)
        : PakFilePath(InPakFilePath)
        , PakFileSize(0)
        , AssetCount(0)
        , LoadedAssetCount(0)
        , bIsMounted(false)
        , MountTime(FDateTime::MinValue())
    {
    }
};

/**
 * 资产查询结果
 */
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoAssetQueryResult
{
    GENERATED_BODY()

    // 找到的资产列表
    UPROPERTY(BlueprintReadOnly, Category = "Query Result")
    TArray<FNeoAssetInfo> Assets;

    // 查询是否成功
    UPROPERTY(BlueprintReadOnly, Category = "Query Result")
    bool bSuccess = false;

    // 查询耗时（毫秒）
    UPROPERTY(BlueprintReadOnly, Category = "Query Result")
    float QueryTimeMs = 0.0f;

    // 错误信息
    UPROPERTY(BlueprintReadOnly, Category = "Query Result")
    FString ErrorMessage;

    FNeoAssetQueryResult()
    {
        bSuccess = false;
        QueryTimeMs = 0.0f;
    }
};

/**
 * Neo资产注册表
 * 管理已加载PAK中的资产信息
 */
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoAssetRegistry : public UObject
{
    GENERATED_BODY()

public:
    UNeoAssetRegistry();

    /**
     * 注册已加载的资产
     * @param AssetPath 资产路径
     * @param PakFilePath PAK文件路径
     * @param AssetClass 资产类型
     * @return 是否成功注册
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool RegisterLoadedAsset(const FSoftObjectPath& AssetPath, const FString& PakFilePath, const FString& AssetClass);

    /**
     * 注销资产
     * @param AssetPath 资产路径
     * @return 是否成功注销
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool UnregisterAsset(const FSoftObjectPath& AssetPath);

    /**
     * 根据PAK文件查找资产
     * @param PakFilePath PAK文件路径
     * @return 查询结果
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    FNeoAssetQueryResult FindAssetsByPak(const FString& PakFilePath);

    /**
     * 根据资产类型查找资产
     * @param AssetClass 资产类型
     * @return 查询结果
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    FNeoAssetQueryResult FindAssetsByClass(const FString& AssetClass);

    /**
     * 根据路径模式查找资产
     * @param PathPattern 路径模式（支持通配符）
     * @return 查询结果
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    FNeoAssetQueryResult FindAssetsByPath(const FString& PathPattern);

    /**
     * 获取资产信息
     * @param AssetPath 资产路径
     * @param OutAssetInfo 输出资产信息
     * @return 是否找到资产
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool GetAssetInfo(const FSoftObjectPath& AssetPath, FNeoAssetInfo& OutAssetInfo);

    /**
     * 注册PAK文件
     * @param PakFilePath PAK文件路径
     * @return 是否成功注册
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool RegisterPakFile(const FString& PakFilePath);

    /**
     * 注销PAK文件
     * @param PakFilePath PAK文件路径
     * @return 是否成功注销
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool UnregisterPakFile(const FString& PakFilePath);

    /**
     * 获取PAK信息
     * @param PakFilePath PAK文件路径
     * @param OutPakInfo 输出PAK信息
     * @return 是否找到PAK
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool GetPakInfo(const FString& PakFilePath, FNeoPakInfo& OutPakInfo);

    /**
     * 获取所有已注册的PAK文件
     * @return PAK文件路径列表
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    TArray<FString> GetAllRegisteredPaks() const;

    /**
     * 获取所有已注册的资产
     * @return 查询结果
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    FNeoAssetQueryResult GetAllRegisteredAssets();

    /**
     * 更新资产加载状态
     * @param AssetPath 资产路径
     * @param bIsLoaded 是否已加载
     * @return 是否成功更新
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    bool UpdateAssetLoadStatus(const FSoftObjectPath& AssetPath, bool bIsLoaded);

    /**
     * 增加资产引用计数
     * @param AssetPath 资产路径
     * @return 新的引用计数
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    int32 AddAssetReference(const FSoftObjectPath& AssetPath);

    /**
     * 减少资产引用计数
     * @param AssetPath 资产路径
     * @return 新的引用计数
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    int32 RemoveAssetReference(const FSoftObjectPath& AssetPath);

    /**
     * 清理所有注册信息
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    void ClearAllRegistrations();

    /**
     * 获取注册统计信息
     * @param OutTotalAssets 总资产数量
     * @param OutLoadedAssets 已加载资产数量
     * @param OutTotalPaks 总PAK数量
     */
    UFUNCTION(BlueprintCallable, Category = "NeoAssetRegistry")
    void GetRegistryStatistics(int32& OutTotalAssets, int32& OutLoadedAssets, int32& OutTotalPaks) const;

protected:
    // 资产信息映射 (AssetPath -> AssetInfo)
    UPROPERTY()
    TMap<FString, FNeoAssetInfo> AssetInfoMap;

    // PAK信息映射 (PakFilePath -> PakInfo)
    UPROPERTY()
    TMap<FString, FNeoPakInfo> PakInfoMap;

    // PAK到资产的映射 (PakFilePath -> AssetPaths)
    TMap<FString, TArray<FString>> PakToAssetsMap;

    // 资产类型到资产的映射 (AssetClass -> AssetPaths)
    TMap<FString, TArray<FString>> ClassToAssetsMap;

private:
    // 内部辅助函数：更新映射关系
    void UpdateMappings(const FString& AssetPath, const FString& PakFilePath, const FString& AssetClass, bool bAdd);

    // 内部辅助函数：路径模式匹配
    bool MatchesPathPattern(const FString& AssetPath, const FString& Pattern) const;
};
