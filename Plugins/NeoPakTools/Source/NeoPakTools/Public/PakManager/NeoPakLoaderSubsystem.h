// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/EngineSubsystem.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "IPlatformFilePak.h"
#include "Async/AsyncWork.h"
#include "NeoPakLoaderSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNeoPakLoaderSubsystem, Log, All);

/**
 * 高级PAK加载状态枚举
 */
UENUM(BlueprintType)
enum class ENeoPakLoadStatus : uint8
{
    NotLoaded       UMETA(DisplayName = "Not Loaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Failed          UMETA(DisplayName = "Failed"),
    Unloading       UMETA(DisplayName = "Unloading")
};

/**
 * PAK加载结果
 */
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakLoadResult
{
    GENERATED_BODY()

    // 加载是否成功
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    // PAK文件路径
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString PakFilePath;

    // 加载状态
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    ENeoPakLoadStatus LoadStatus = ENeoPakLoadStatus::NotLoaded;

    // 错误信息
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    // 加载的资产数量
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 LoadedAssetCount = 0;

    // 加载耗时（毫秒）
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float LoadTimeMs = 0.0f;

    FNeoPakLoadResult()
    {
        bSuccess = false;
        LoadStatus = ENeoPakLoadStatus::NotLoaded;
        LoadedAssetCount = 0;
        LoadTimeMs = 0.0f;
    }
};

/**
 * 异步PAK加载任务
 */
class NEOPAKTOOLS_API FNeoPakLoadTask : public FNonAbandonableTask
{
public:
    FNeoPakLoadTask(const FString& InPakPath, bool bInPreloadDependencies = false);

    // FNonAbandonableTask interface
    void DoWork();
    FORCEINLINE TStatId GetStatId() const { RETURN_QUICK_DECLARE_CYCLE_STAT(FNeoPakLoadTask, STATGROUP_ThreadPoolAsyncTasks); }

    // 获取加载结果
    FNeoPakLoadResult GetResult() const { return LoadResult; }

private:
    FString PakPath;
    bool bPreloadDependencies;
    FNeoPakLoadResult LoadResult;

    // 执行实际的PAK加载
    bool LoadPakFile();

    // 加载IoStore容器
    bool LoadIoStoreContainer(const FString& PakPath, const FString& UtocPath);

    // 加载传统PAK文件
    bool LoadTraditionalPak(const FString& PakPath);

    // 预加载依赖资产
    void PreloadDependencies();
};

/**
 * Neo PAK加载器子系统
 * 提供全局的PAK管理功能，包括异步加载、预加载、状态管理等
 */
UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoPakLoaderSubsystem : public UEngineSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * 获取子系统实例
     * @return 子系统实例
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader", CallInEditor)
    static UNeoPakLoaderSubsystem* Get();

    /**
     * 异步加载PAK文件
     * @param PakFilePath PAK文件路径
     * @param bPreloadDependencies 是否预加载依赖
     * @return 异步任务句柄
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    int32 LoadPakAsync(const FString& PakFilePath, bool bPreloadDependencies = false);

    /**
     * 同步加载PAK文件
     * @param PakFilePath PAK文件路径
     * @param bPreloadDependencies 是否预加载依赖
     * @return 加载结果
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    FNeoPakLoadResult LoadPakSync(const FString& PakFilePath, bool bPreloadDependencies = false);

    /**
     * 卸载PAK文件
     * @param PakFilePath PAK文件路径
     * @return 是否成功卸载
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool UnloadPak(const FString& PakFilePath);

    /**
     * 获取PAK加载状态
     * @param PakFilePath PAK文件路径
     * @return 加载状态
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    ENeoPakLoadStatus GetLoadStatus(const FString& PakFilePath) const;

    /**
     * 获取异步加载任务的结果
     * @param TaskHandle 任务句柄
     * @param OutResult 输出结果
     * @return 任务是否完成
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool GetAsyncLoadResult(int32 TaskHandle, FNeoPakLoadResult& OutResult);

    /**
     * 取消异步加载任务
     * @param TaskHandle 任务句柄
     * @return 是否成功取消
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool CancelAsyncLoad(int32 TaskHandle);

    /**
     * 获取所有已加载的PAK文件
     * @return PAK文件路径列表
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    TArray<FString> GetLoadedPakFiles() const;

    /**
     * 预加载指定PAK的依赖资产
     * @param PakFilePath PAK文件路径
     * @return 是否成功
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool PreloadDependencies(const FString& PakFilePath);

    /**
     * 清理所有加载状态
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    void ClearAllLoadStates();

    /**
     * 获取加载统计信息
     * @param OutTotalLoaded 总加载数量
     * @param OutTotalFailed 总失败数量
     * @param OutAverageLoadTime 平均加载时间
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    void GetLoadStatistics(int32& OutTotalLoaded, int32& OutTotalFailed, float& OutAverageLoadTime) const;

    /**
     * 批量加载PAK文件
     * @param PakFilePaths PAK文件路径列表
     * @param bPreloadDependencies 是否预加载依赖
     * @return 批量加载任务句柄列表
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    TArray<int32> LoadPaksBatch(const TArray<FString>& PakFilePaths, bool bPreloadDependencies = false);

    /**
     * 检查是否有正在进行的加载任务
     * @return 是否有活动任务
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool HasActiveLoadTasks() const;

    /**
     * 检查PAK系统是否可用
     * @return PAK系统是否已初始化并可用
     */
    UFUNCTION(BlueprintCallable, Category = "NeoPakLoader")
    bool IsPakSystemAvailable() const;

protected:
    // PAK文件状态映射
    UPROPERTY()
    TMap<FString, ENeoPakLoadStatus> PakStatusMap;

    // 异步加载任务映射
    TMap<int32, TSharedPtr<FAsyncTask<FNeoPakLoadTask>>> AsyncTasks;

    // 下一个任务句柄
    int32 NextTaskHandle;

    // 加载统计
    int32 TotalLoadedCount;
    int32 TotalFailedCount;
    float TotalLoadTime;

    // 生成新的任务句柄
    int32 GenerateTaskHandle();

    // 更新PAK状态
    void UpdatePakStatus(const FString& PakFilePath, ENeoPakLoadStatus NewStatus);

    // 清理完成的异步任务
    void CleanupCompletedTasks();

    // 内部同步加载实现
    FNeoPakLoadResult LoadPakInternal(const FString& PakFilePath, bool bPreloadDependencies);

    // 定期清理任务（手动调用）
    void PeriodicCleanup();
};
