// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/SkeletalMesh.h"
#include "Animation/Skeleton.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimMontage.h"
#include "Config/NeoSkeletonPakConfig.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNeoAssetCompatibilityValidator, Log, All);

/**
 * 资产兼容性验证结果
 */
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoCompatibilityCheckResult
{
    GENERATED_BODY()

    // 检查是否通过
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bIsCompatible = false;

    // 错误信息列表
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> ErrorMessages;

    // 警告信息列表
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> WarningMessages;

    // 详细信息
    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString DetailedInfo;

    FNeoCompatibilityCheckResult()
    {
        bIsCompatible = false;
    }
};

/**
 * 资产兼容性验证器
 * 用于检查不同类型资产之间的兼容性
 */
class NEOPAKTOOLS_API FNeoAssetCompatibilityValidator
{
public:
    /**
     * 检查动画序列与骨骼配置的兼容性
     * @param AnimSequence 动画序列资产
     * @param SkeletonConfig 骨骼配置
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult IsAnimationCompatibleWithSkeletonConfig(
        UAnimSequence* AnimSequence, 
        UNeoSkeletonPakConfig* SkeletonConfig
    );

    /**
     * 检查动画蒙太奇与骨骼配置的兼容性
     * @param AnimMontage 动画蒙太奇资产
     * @param SkeletonConfig 骨骼配置
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult IsAnimMontageCompatibleWithSkeletonConfig(
        UAnimMontage* AnimMontage, 
        UNeoSkeletonPakConfig* SkeletonConfig
    );

    /**
     * 检查骨骼网格与骨骼配置的兼容性
     * @param SkeletalMesh 骨骼网格资产
     * @param SkeletonConfig 骨骼配置
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult IsSkeletalMeshCompatibleWithSkeletonConfig(
        USkeletalMesh* SkeletalMesh, 
        UNeoSkeletonPakConfig* SkeletonConfig
    );

    /**
     * 检查两个骨骼资产的兼容性
     * @param Skeleton1 第一个骨骼资产
     * @param Skeleton2 第二个骨骼资产
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult AreSkeletonsCompatible(
        USkeleton* Skeleton1, 
        USkeleton* Skeleton2
    );

    /**
     * 检查动画序列与骨骼的兼容性
     * @param AnimSequence 动画序列
     * @param Skeleton 骨骼资产
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult IsAnimationCompatibleWithSkeleton(
        UAnimSequence* AnimSequence, 
        USkeleton* Skeleton
    );

    /**
     * 检查骨骼网格与骨骼的兼容性
     * @param SkeletalMesh 骨骼网格
     * @param Skeleton 骨骼资产
     * @return 兼容性检查结果
     */
    static FNeoCompatibilityCheckResult IsSkeletalMeshCompatibleWithSkeleton(
        USkeletalMesh* SkeletalMesh, 
        USkeleton* Skeleton
    );

private:
    /**
     * 比较两个骨骼的骨骼层次结构
     * @param Skeleton1 第一个骨骼
     * @param Skeleton2 第二个骨骼
     * @param OutMissingBones 缺失的骨骼名称列表
     * @param OutExtraBones 额外的骨骼名称列表
     * @return 是否兼容
     */
    static bool CompareBoneHierarchy(
        USkeleton* Skeleton1, 
        USkeleton* Skeleton2,
        TArray<FString>& OutMissingBones,
        TArray<FString>& OutExtraBones
    );

    /**
     * 检查动画轨道与骨骼的兼容性
     * @param AnimSequence 动画序列
     * @param Skeleton 骨骼资产
     * @param OutMissingTracks 缺失的动画轨道
     * @param OutUnusedTracks 未使用的动画轨道
     * @return 是否兼容
     */
    static bool CheckAnimationTracks(
        UAnimSequence* AnimSequence,
        USkeleton* Skeleton,
        TArray<FString>& OutMissingTracks,
        TArray<FString>& OutUnusedTracks
    );

    /**
     * 获取骨骼的所有骨骼名称
     * @param Skeleton 骨骼资产
     * @return 骨骼名称列表
     */
    static TArray<FString> GetBoneNames(USkeleton* Skeleton);

    /**
     * 获取动画序列的所有轨道名称
     * @param AnimSequence 动画序列
     * @return 轨道名称列表
     */
    static TArray<FString> GetAnimationTrackNames(UAnimSequence* AnimSequence);
};
