// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoClothingPakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"
#include "Utils/NeoAssetDependencyResolver.h"

#define LOCTEXT_NAMESPACE "NeoClothingPakConfigActions"

FText FNeoClothingPakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoClothingPakConfig", "Clothing Pack Config");
}

FColor FNeoClothingPakConfigActions::GetTypeColor() const
{
    return FColor(255, 128, 255); // Purple
}

UClass* FNeoClothingPakConfigActions::GetSupportedClass() const
{
    return UNeoClothingPakConfig::StaticClass();
}

uint32 FNeoClothingPakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoClothingPakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    auto ClothingConfigs = GetTypedWeakObjectPtrs<UNeoClothingPakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("ClothingPakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("ClothingPakConfig_ExecutePackagingTooltip", "Execute packaging for this clothing configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoClothingPakConfigActions::ExecutePackaging, ClothingConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoClothingPakConfigActions::CanExecutePackaging, ClothingConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("ClothingPakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("ClothingPakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoClothingPakConfigActions::ExecuteValidateConfig, ClothingConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("ClothingPakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("ClothingPakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoClothingPakConfigActions::ExecuteCheckDirectoryDependencies, ClothingConfigs)
        )
    );
}

void FNeoClothingPakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoClothingPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoClothingPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for clothing config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged clothing config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Failed to package clothing config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoClothingPakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoClothingPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoClothingPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating clothing config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoClothingPakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoClothingPakConfig>> Objects)
{
    FNeoAssetDependencyResolver DependencyResolver;
    int32 PassedCount = 0;
    int32 FailedCount = 0;
    TArray<FString> AllErrorMessages;

    for (auto& WeakObject : Objects)
    {
        if (UNeoClothingPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for clothing config: %s"), *Config->GetName());

            // 执行依赖检查
            FNeoDependencyCheckResult Result = DependencyResolver.CheckDataAssetDirectoryDependencies(Config, false);

            if (Result.bCheckPassed)
            {
                PassedCount++;
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Dependency check passed for: %s"), *Config->ConfigName);
            }
            else
            {
                FailedCount++;
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependency check failed for: %s"), *Config->ConfigName);

                // 收集错误信息
                for (const FString& ErrorMsg : Result.ErrorMessages)
                {
                    AllErrorMessages.Add(FString::Printf(TEXT("[%s] %s"), *Config->ConfigName, *ErrorMsg));
                }
            }
        }
    }

    // 显示详细结果
    FString DetailedMessage = FString::Printf(TEXT("Clothing Dependency Check Results:\n\nPassed: %d\nFailed: %d\n"),
                                             PassedCount, FailedCount);

    if (AllErrorMessages.Num() > 0)
    {
        DetailedMessage += TEXT("\nDetailed Errors:\n");
        for (int32 i = 0; i < FMath::Min(AllErrorMessages.Num(), 20); ++i)
        {
            DetailedMessage += FString::Printf(TEXT("• %s\n"), *AllErrorMessages[i]);
        }

        if (AllErrorMessages.Num() > 20)
        {
            DetailedMessage += FString::Printf(TEXT("... and %d more errors\n"), AllErrorMessages.Num() - 20);
        }
    }

    FMessageDialog::Open(
        FailedCount > 0 ? EAppMsgType::Ok : EAppMsgType::Ok,
        FText::FromString(DetailedMessage),
        &LOCTEXT("DependencyCheckResultTitle", "Directory Dependency Check Results")
    );
}

bool FNeoClothingPakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoClothingPakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoClothingPakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return Objects.Num() > 0;
}

#undef LOCTEXT_NAMESPACE
