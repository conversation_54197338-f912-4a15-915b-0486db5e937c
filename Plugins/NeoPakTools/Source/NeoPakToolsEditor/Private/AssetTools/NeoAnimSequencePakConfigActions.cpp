// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoAnimSequencePakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"

#define LOCTEXT_NAMESPACE "NeoAnimSequencePakConfigActions"

FText FNeoAnimSequencePakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoAnimSequencePakConfig", "Animation Sequence Pack Config");
}

FColor FNeoAnimSequencePakConfigActions::GetTypeColor() const
{
    return FColor(128, 128, 255); // Blue
}

UClass* FNeoAnimSequencePakConfigActions::GetSupportedClass() const
{
    return UNeoAnimSequencePakConfig::StaticClass();
}

uint32 FNeoAnimSequencePakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoAnimSequencePakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> AnimSequenceConfigs = GetTypedWeakObjectPtrs<UNeoAnimSequencePakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimSequencePakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("AnimSequencePakConfig_ExecutePackagingTooltip", "Execute the packaging process for this animation sequence configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimSequencePakConfigActions::ExecutePackaging, AnimSequenceConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoAnimSequencePakConfigActions::CanExecutePackaging, AnimSequenceConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimSequencePakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("AnimSequencePakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimSequencePakConfigActions::ExecuteValidateConfig, AnimSequenceConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("AnimSequencePakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("AnimSequencePakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoAnimSequencePakConfigActions::ExecuteCheckDirectoryDependencies, AnimSequenceConfigs)
        )
    );
}

bool FNeoAnimSequencePakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimSequencePakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return true;
}

void FNeoAnimSequencePakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimSequencePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for animation sequence config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Packaging completed successfully: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Packaging failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimSequencePakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimSequencePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating animation sequence config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoAnimSequencePakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoAnimSequencePakConfig>> Objects)
{
    FNeoAssetDependencyResolver DependencyResolver;
    int32 PassedCount = 0;
    int32 FailedCount = 0;
    TArray<FString> AllErrorMessages;

    for (auto& WeakObject : Objects)
    {
        if (UNeoAnimSequencePakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for animation sequence config: %s"), *Config->GetName());

            // 执行依赖检查
            FNeoDependencyCheckResult Result = DependencyResolver.CheckDataAssetDirectoryDependencies(Config, false);

            if (Result.bCheckPassed)
            {
                PassedCount++;
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Dependency check passed for: %s"), *Config->ConfigName);
            }
            else
            {
                FailedCount++;
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependency check failed for: %s"), *Config->ConfigName);

                // 收集错误信息
                for (const FString& ErrorMsg : Result.ErrorMessages)
                {
                    AllErrorMessages.Add(FString::Printf(TEXT("[%s] %s"), *Config->ConfigName, *ErrorMsg));
                }
            }
        }
    }

    // 显示详细结果
    FString DetailedMessage = FString::Printf(TEXT("Animation Sequence Dependency Check Results:\n\nPassed: %d\nFailed: %d\n"),
                                             PassedCount, FailedCount);

    if (AllErrorMessages.Num() > 0)
    {
        DetailedMessage += TEXT("\nDetailed Errors:\n");
        for (int32 i = 0; i < FMath::Min(AllErrorMessages.Num(), 20); ++i) // 限制显示前20个错误
        {
            DetailedMessage += FString::Printf(TEXT("• %s\n"), *AllErrorMessages[i]);
        }

        if (AllErrorMessages.Num() > 20)
        {
            DetailedMessage += FString::Printf(TEXT("... and %d more errors\n"), AllErrorMessages.Num() - 20);
        }
    }

    FMessageDialog::Open(
        FailedCount > 0 ? EAppMsgType::Ok : EAppMsgType::Ok,
        FText::FromString(DetailedMessage),
        &LOCTEXT("DependencyCheckResultTitle", "Directory Dependency Check Results")
    );
}

#undef LOCTEXT_NAMESPACE
