// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoMapPakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"
#include "Utils/NeoAssetDependencyResolver.h"

#define LOCTEXT_NAMESPACE "NeoMapPakConfigActions"

FText FNeoMapPakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoMapPakConfig", "Map Pack Config");
}

FColor FNeoMapPakConfigActions::GetTypeColor() const
{
    return FColor(255, 255, 128); // Yellow
}

UClass* FNeoMapPakConfigActions::GetSupportedClass() const
{
    return UNeoMapPakConfig::StaticClass();
}

uint32 FNeoMapPakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoMapPakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    auto MapConfigs = GetTypedWeakObjectPtrs<UNeoMapPakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("MapPakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("MapPakConfig_ExecutePackagingTooltip", "Execute packaging for this map configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoMapPakConfigActions::ExecutePackaging, MapConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoMapPakConfigActions::CanExecutePackaging, MapConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("MapPakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("MapPakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoMapPakConfigActions::ExecuteValidateConfig, MapConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("MapPakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("MapPakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoMapPakConfigActions::ExecuteCheckDirectoryDependencies, MapConfigs)
        )
    );
}

void FNeoMapPakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoMapPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoMapPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for map config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged map config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Failed to package map config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoMapPakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoMapPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoMapPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating map config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoMapPakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoMapPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoMapPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for map config: %s"), *Config->GetName());
            
            // TODO: Implement dependency checking logic
            FMessageDialog::Open(EAppMsgType::Ok, 
                FText::Format(LOCTEXT("DependencyCheckPlaceholder", "Directory dependency check for: {0}\n(Implementation pending)"), 
                FText::FromString(Config->ConfigName)));
        }
    }
}

bool FNeoMapPakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoMapPakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoMapPakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return Objects.Num() > 0;
}

#undef LOCTEXT_NAMESPACE
