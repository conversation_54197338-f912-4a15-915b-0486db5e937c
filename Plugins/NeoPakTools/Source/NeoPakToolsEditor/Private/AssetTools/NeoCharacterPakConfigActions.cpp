// Copyright Epic Games, Inc. All Rights Reserved.

#include "AssetTools/NeoCharacterPakConfigActions.h"
#include "NeoPakToolsEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Misc/MessageDialog.h"
#include "Utils/NeoAssetDependencyResolver.h"

#define LOCTEXT_NAMESPACE "NeoCharacterPakConfigActions"

FText FNeoCharacterPakConfigActions::GetName() const
{
    return LOCTEXT("AssetTypeActions_NeoCharacterPakConfig", "Character Pack Config");
}

FColor FNeoCharacterPakConfigActions::GetTypeColor() const
{
    return FColor(128, 255, 128); // Green
}

UClass* FNeoCharacterPakConfigActions::GetSupportedClass() const
{
    return UNeoCharacterPakConfig::StaticClass();
}

uint32 FNeoCharacterPakConfigActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FNeoCharacterPakConfigActions::GetActions(const TArray<UObject*>& InObjects, FMenuBuilder& MenuBuilder)
{
    auto CharacterConfigs = GetTypedWeakObjectPtrs<UNeoCharacterPakConfig>(InObjects);

    MenuBuilder.AddMenuEntry(
        LOCTEXT("CharacterPakConfig_ExecutePackaging", "Execute Packaging"),
        LOCTEXT("CharacterPakConfig_ExecutePackagingTooltip", "Execute packaging for this character configuration"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoCharacterPakConfigActions::ExecutePackaging, CharacterConfigs),
            FCanExecuteAction::CreateSP(this, &FNeoCharacterPakConfigActions::CanExecutePackaging, CharacterConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("CharacterPakConfig_ValidateConfig", "Validate Configuration"),
        LOCTEXT("CharacterPakConfig_ValidateConfigTooltip", "Validate the configuration settings"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoCharacterPakConfigActions::ExecuteValidateConfig, CharacterConfigs)
        )
    );

    MenuBuilder.AddMenuEntry(
        LOCTEXT("CharacterPakConfig_CheckDependencies", "Check Directory Dependencies"),
        LOCTEXT("CharacterPakConfig_CheckDependenciesTooltip", "Check if all dependencies are in the correct directory"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateSP(this, &FNeoCharacterPakConfigActions::ExecuteCheckDirectoryDependencies, CharacterConfigs)
        )
    );
}

void FNeoCharacterPakConfigActions::ExecutePackaging(TArray<TWeakObjectPtr<UNeoCharacterPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoCharacterPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Executing packaging for character config: %s"), *Config->GetName());
            
            if (Config->ExecutePackaging())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingSuccess", "Successfully packaged character config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("PackagingFailed", "Failed to package character config: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoCharacterPakConfigActions::ExecuteValidateConfig(TArray<TWeakObjectPtr<UNeoCharacterPakConfig>> Objects)
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoCharacterPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Validating character config: %s"), *Config->GetName());
            
            if (Config->ValidateConfiguration())
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationSuccess", "Configuration is valid: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
            else
            {
                FMessageDialog::Open(EAppMsgType::Ok, 
                    FText::Format(LOCTEXT("ValidationFailed", "Configuration validation failed: {0}"), 
                    FText::FromString(Config->ConfigName)));
            }
        }
    }
}

void FNeoCharacterPakConfigActions::ExecuteCheckDirectoryDependencies(TArray<TWeakObjectPtr<UNeoCharacterPakConfig>> Objects)
{
    FNeoAssetDependencyResolver DependencyResolver;
    int32 PassedCount = 0;
    int32 FailedCount = 0;
    TArray<FString> AllErrorMessages;

    for (auto& WeakObject : Objects)
    {
        if (UNeoCharacterPakConfig* Config = WeakObject.Get())
        {
            UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Checking directory dependencies for character config: %s"), *Config->GetName());

            // 执行依赖检查
            FNeoDependencyCheckResult Result = DependencyResolver.CheckDataAssetDirectoryDependencies(Config, false);

            if (Result.bCheckPassed)
            {
                PassedCount++;
                UE_LOG(LogNeoPakToolsEditor, Log, TEXT("Dependency check passed for: %s"), *Config->ConfigName);
            }
            else
            {
                FailedCount++;
                UE_LOG(LogNeoPakToolsEditor, Warning, TEXT("Dependency check failed for: %s"), *Config->ConfigName);

                // 收集错误信息
                for (const FString& ErrorMsg : Result.ErrorMessages)
                {
                    AllErrorMessages.Add(FString::Printf(TEXT("[%s] %s"), *Config->ConfigName, *ErrorMsg));
                }
            }
        }
    }

    // 显示详细结果
    FString DetailedMessage = FString::Printf(TEXT("Character Dependency Check Results:\n\nPassed: %d\nFailed: %d\n"),
                                             PassedCount, FailedCount);

    if (AllErrorMessages.Num() > 0)
    {
        DetailedMessage += TEXT("\nDetailed Errors:\n");
        for (int32 i = 0; i < FMath::Min(AllErrorMessages.Num(), 20); ++i)
        {
            DetailedMessage += FString::Printf(TEXT("• %s\n"), *AllErrorMessages[i]);
        }

        if (AllErrorMessages.Num() > 20)
        {
            DetailedMessage += FString::Printf(TEXT("... and %d more errors\n"), AllErrorMessages.Num() - 20);
        }
    }

    FMessageDialog::Open(
        FailedCount > 0 ? EAppMsgType::Ok : EAppMsgType::Ok,
        FText::FromString(DetailedMessage),
        &LOCTEXT("DependencyCheckResultTitle", "Directory Dependency Check Results")
    );
}

bool FNeoCharacterPakConfigActions::CanExecutePackaging(TArray<TWeakObjectPtr<UNeoCharacterPakConfig>> Objects) const
{
    for (auto& WeakObject : Objects)
    {
        if (UNeoCharacterPakConfig* Config = WeakObject.Get())
        {
            if (!Config->ValidateConfiguration())
            {
                return false;
            }
        }
    }
    return Objects.Num() > 0;
}

#undef LOCTEXT_NAMESPACE
